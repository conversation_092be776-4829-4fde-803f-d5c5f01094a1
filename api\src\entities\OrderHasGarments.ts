import { BeforeInsert, Before<PERSON>p<PERSON>, Column, En<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from "typeorm";
import { Garment } from "./Garment";
import { Order } from "./Order";

@Entity("order_has_garments")
export class OrderHasGarments {
    @PrimaryColumn({ name: "order_id" })
    order_id!: number;

    @PrimaryColumn({ name: "garment_id" })
    garment_id!: number;

    @ManyToOne(() => Order, order => order.garments, { nullable: false, onDelete: "CASCADE" })
    @JoinColumn({ name: "order_id" })
    order!: Order;

    @ManyToOne(() => Garment, garment => garment.orderHasGarments, { nullable: false, onDelete: "CASCADE" })
    @JoinColumn({ name: "garment_id" })
    garment!: Garment;

    @Column()
    quantity!: number;

    @BeforeInsert()
    @BeforeUpdate()
    updatePrimaryKeys() {
        if (this.order && this.order.id) {
            this.order_id = this.order.id;
        }
        if (this.garment && this.garment.id) {
            this.garment_id = this.garment.id;
        }
    }
}