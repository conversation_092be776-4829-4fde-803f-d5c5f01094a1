import { AppDataSource } from "../data-source";
import { ICreateOrderDto } from "../dto/OrderDto";
import { Garment } from "../entities/Garment";
import { Order } from "../entities/Order";
import { garmentRepository } from "./garmentRepository";
import { orderHasGarmentsRepository } from "./orderHasGamentsRepository";

interface IOrderHasGarments {
    order: Order;
    garment: Garment;
    order_id: number;
    garment_id: number;
    quantity: number;
}

export const orderRepository = AppDataSource.getRepository(Order).extend({
    async saveOrderCompletely(order: ICreateOrderDto) {
        const savedOrder = await this.save(order);
        
        const garments = order.garments;
        garments.forEach(async (garment) => {
            const garmentInstance = await garmentRepository.findOneBy({ id: garment.id });
            if (!garmentInstance) {
                throw new Error(`Garment with id ${garment.id} not found`);
            }

            const newOrderHasGarmentInstance: IOrderHasGarments = {
                order: savedOrder,
                garment: garmentInstance,
                order_id: savedOrder.id,
                garment_id: garmentInstance.id,
                quantity: garment.quantity
            }
            await orderHasGarmentsRepository.save(newOrderHasGarmentInstance);
        });

        return savedOrder;
    }
});